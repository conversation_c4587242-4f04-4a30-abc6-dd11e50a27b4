import satori from 'satori';
import { Resvg } from '@resvg/resvg-js';
import { html as toReactNode } from 'satori-html';

const height = 630;
const width = 1200;
const CACHE_CONTROL = 'public, max-age=604800, stale-while-revalidate=86400';

function generateCacheKey(params) {
	const sortedParams = Object.keys(params)
		.sort()
		.map(key => `${key}=${params[key]}`)
		.join('&');

	return Buffer.from(sortedParams).toString('base64').replace(/[/+=]/g, '');
}

function getStaticTemplate() {
	return `
		<div style="
			display: flex;
			width: 100%;
			height: 100%;
			position: relative;
		">
			<div style="
				display: flex;
				flex-direction: column;
				width: 100%;
				height: 100%;
				padding: 64px 96px;
				justify-content: center;
				align-items: center;
				background: linear-gradient(135deg, #111827 0%, #581c87 50%, #111827 100%);
			">

				<!-- Main Title -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 32px;
				">
					<div style="
						color: white;
						font-size: 96px;
						font-weight: bold;
						text-align: center;
					">
						anithing.moe
					</div>
					<div style="
						width: 128px;
						height: 4px;
						background: linear-gradient(90deg, #9333ea 0%, #0ea5e9 50%, #ec4899 100%);
						border-radius: 2px;
						margin-top: 16px;
					"></div>
				</div>

				<!-- Subtitle -->
				<div style="
					color: #7dd3fc;
					font-size: 48px;
					font-weight: 600;
					margin-bottom: 24px;
					text-align: center;
					max-width: 1000px;
				">
					Your ultimate gateway to Japanese media
				</div>

				<!-- Description -->
				<div style="
					color: #cbd5e1;
					font-size: 28px;
					text-align: center;
					max-width: 1000px;
					line-height: 1.5;
					margin-bottom: 32px;
				">
					Search, track, and manage anime, manga, VNs, and LNs across all platforms
				</div>

				<!-- Feature badges -->
				<div style="
					display: flex;
					flex-wrap: wrap;
					justify-content: center;
					gap: 16px;
					margin-top: 32px;
				">
					<div style="
						display: flex;
						align-items: center;
						padding: 12px 24px;
						background: rgba(147, 51, 234, 0.3);
						border-radius: 50px;
						border: 1px solid rgba(147, 51, 234, 0.3);
					">
						<div style="
							color: #c4b5fd;
							font-size: 24px;
							font-weight: 500;
						">🔍 Unified Search</div>
					</div>
					<div style="
						display: flex;
						align-items: center;
						padding: 12px 24px;
						background: rgba(14, 165, 233, 0.3);
						border-radius: 50px;
						border: 1px solid rgba(14, 165, 233, 0.3);
					">
						<div style="
							color: #7dd3fc;
							font-size: 24px;
							font-weight: 500;
						">📚 Centralized Lists</div>
					</div>
					<div style="
						display: flex;
						align-items: center;
						padding: 12px 24px;
						background: rgba(236, 72, 153, 0.3);
						border-radius: 50px;
						border: 1px solid rgba(236, 72, 153, 0.3);
					">
						<div style="
							color: #f9a8d4;
							font-size: 24px;
							font-weight: 500;
						">👀 Track Friends</div>
					</div>
				</div>

				<!-- Bottom accent -->
				<div style="
					color: #94a3b8;
					font-size: 20px;
					margin-top: 40px;
				">All your media, aniwhere.</div>
			</div>
		</div>
	`;
}

function getDynamicTemplate(params) {
	return `
		<div style="
			display: flex;
			width: 100%;
			height: 100%;
		">
			<div style="
				display: flex;
				flex-direction: column;
				width: 100%;
				height: 100%;
				padding: 64px 96px;
				justify-content: center;
				align-items: center;
				background: linear-gradient(135deg, #111827 0%, #581c87 50%, #111827 100%);
			">

				<!-- Main Title -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 32px;
				">
					<div style="
						color: white;
						font-size: 72px;
						font-weight: bold;
						text-align: center;
					">
						${params.title || 'anithing.moe'}
					</div>
					<div style="
						width: 128px;
						height: 4px;
						background: linear-gradient(90deg, #9333ea 0%, #0ea5e9 50%, #ec4899 100%);
						border-radius: 2px;
						margin-top: 16px;
					"></div>
				</div>

				${params.subtitle ? `
					<div style="
						color: #7dd3fc;
						font-size: 40px;
						font-weight: 600;
						margin-bottom: 24px;
						text-align: center;
						max-width: 1000px;
					">
						${params.subtitle}
					</div>
				` : ''}

				${params.description ? `
					<div style="
						color: #cbd5e1;
						font-size: 24px;
						text-align: center;
						max-width: 1000px;
						line-height: 1.5;
						margin-bottom: 32px;
					">
						${params.description}
					</div>
				` : ''}

				${params.content ? `
					<div style="
						color: #e2e8f0;
						font-size: 20px;
						text-align: center;
						max-width: 900px;
						line-height: 1.4;
					">
						${params.content}
					</div>
				` : ''}

				<div style="
					color: #94a3b8;
					font-size: 18px;
					margin-top: 40px;
				">anithing.moe</div>
			</div>
		</div>
	`;
}

export async function GET({ url, setHeaders }) {
	try {
		// Extract parameters from URL
		const type = url.searchParams.get('type') || 'static';
		const title = url.searchParams.get('title');
		const subtitle = url.searchParams.get('subtitle');
		const description = url.searchParams.get('description');
		const content = url.searchParams.get('content');

		// Parameters for caching
		const params = {
			type,
			...(title && { title }),
			...(subtitle && { subtitle }),
			...(description && { description }),
			...(content && { content })
		};

		// Generate cache key and set headers
		const cacheKey = generateCacheKey(params);
		setHeaders({
			'Cache-Control': CACHE_CONTROL,
			'ETag': `"${cacheKey}"`,
			'Content-Type': 'image/png'
		});

		// Load font
		const fontFile = await fetch('http://pixeldrain.com/api/file/52yBhNXR');
		if (!fontFile.ok) {
			throw new Error('Failed to load font');
		}
		const fontData = await fontFile.arrayBuffer();

		// Choose template based on type
		let htmlString;
		if (type === 'dynamic') {
			htmlString = getDynamicTemplate({
				title,
				subtitle,
				description,
				content
			});
		} else {
			// Default to static template
			htmlString = getStaticTemplate();
		}

		// Convert HTML to Satori-compatible object
		const element = toReactNode(htmlString);

		// Generate SVG using Satori
		const svg = await satori(element, {
			width,
			height,
			fonts: [
				{
					name: 'Gilroy-SemiBold',
					data: fontData,
					weight: 600,
					style: 'normal'
				}
			]
		});

		// Convert SVG to PNG
		const resvg = new Resvg(svg, {
			fitTo: {
				mode: 'width',
				value: width
			}
		});

		const image = resvg.render();

		return new Response(image.asPng(), {
			headers: {
				'Content-Type': 'image/png',
				'Cache-Control': CACHE_CONTROL,
				'ETag': `"${cacheKey}"`
			}
		});

	} catch (error) {
		console.error('Error generating OG image:', error);
		return new Response('Error generating image', { status: 500 });
	}
}
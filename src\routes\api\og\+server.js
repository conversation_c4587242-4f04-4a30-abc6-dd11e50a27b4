import satori from 'satori';
import { Resvg } from '@resvg/resvg-js';
import { html as toReactNode } from 'satori-html';

const height = 630;
const width = 1200;

export async function GET() {
	try {
		// Simple HTML template for hello world
		const htmlString = `
			<div style="
				height: 100%;
				width: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				background-color: #1a1a1a;
				font-family: Arial;
				font-size: 60px;
				font-weight: 600;
				color: white;
			">
				<div style="margin-bottom: 20px;">Hello World!</div>
				<div style="font-size: 40px; color: #888;">anithing.moe</div>
			</div>
		`;

		// Convert HTML to Satori-compatible object
		const element = toReactNode(htmlString);

		// Generate SVG using Satori
		const svg = await satori(element, {
			width,
			height,
			fonts: [
				{
					name: 'Arial',
					data: await fetch('http://pixeldrain.com/api/file/52yBhNXR').then(res => res.arrayBuffer()),
					weight: 400,
					style: 'normal'
				}
			]
		});

		// Convert SVG to PNG
		const resvg = new Resvg(svg, {
			fitTo: {
				mode: 'width',
				value: width
			}
		});

		const image = resvg.render();

		return new Response(image.asPng(), {
			headers: {
				'Content-Type': 'image/png',
				'Cache-Control': 'public, max-age=3600'
			}
		});
	} catch (error) {
		console.error('Error generating OG image:', error);
		return new Response('Error generating image', { status: 500 });
	}
}
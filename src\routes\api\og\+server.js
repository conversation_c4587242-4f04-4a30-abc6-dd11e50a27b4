import satori from 'satori';
import { Resvg } from '@resvg/resvg-js';
import { html as toReactNode } from 'satori-html';

const height = 630;
const width = 1200;
const CACHE_CONTROL = 'public, max-age=604800, stale-while-revalidate=86400';

function generateCacheKey(params) {
	const sortedParams = Object.keys(params)
		.sort()
		.map(key => `${key}=${params[key]}`)
		.join('&');

	return Buffer.from(sortedParams).toString('base64').replace(/[/+=]/g, '');
}

function getStaticTemplate() {
	return `
		<div style="
			display: flex;
			width: 100%;
			height: 100%;
			background: linear-gradient(135deg, #111827 0%, #581c87 50%, #111827 100%);
			justify-content: center;
			align-items: center;
		">
			<div style="
				display: flex;
				flex-direction: column;
				align-items: center;
				text-align: center;
				padding: 64px;
			">
				<div style="
					color: white;
					font-size: 96px;
					font-weight: bold;
					margin-bottom: 16px;
				">
					anithing.moe
				</div>
				<div style="
					width: 128px;
					height: 4px;
					background: linear-gradient(90deg, #9333ea 0%, #0ea5e9 50%, #ec4899 100%);
					border-radius: 2px;
					margin-bottom: 32px;
				"></div>
				<div style="
					color: #7dd3fc;
					font-size: 48px;
					font-weight: 600;
					margin-bottom: 24px;
					max-width: 1000px;
				">
					Your ultimate gateway to Japanese media
				</div>
				<div style="
					color: #cbd5e1;
					font-size: 28px;
					max-width: 1000px;
					line-height: 1.5;
					margin-bottom: 32px;
				">
					Search, track, and manage anime, manga, VNs, and LNs across all platforms
				</div>
				<div style="
					color: #94a3b8;
					font-size: 20px;
					margin-top: 40px;
				">All your media, aniwhere.</div>
			</div>
		</div>
	`;
}

function getDynamicTemplate(params) {
	return `
		<div style="
			display: flex;
			width: 100%;
			height: 100%;
		">
			<div style="
				display: flex;
				flex-direction: column;
				width: 100%;
				height: 100%;
				padding: 64px 96px;
				justify-content: center;
				align-items: center;
				background: linear-gradient(135deg, #111827 0%, #581c87 50%, #111827 100%);
			">
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					text-align: center;
				">
					<div style="
						color: white;
						font-size: 72px;
						font-weight: bold;
						margin-bottom: 16px;
					">
						${params.title || 'anithing.moe'}
					</div>
					<div style="
						width: 128px;
						height: 4px;
						background: linear-gradient(90deg, #9333ea 0%, #0ea5e9 50%, #ec4899 100%);
						border-radius: 2px;
						margin-bottom: 32px;
					"></div>
					${params.subtitle ? `
						<div style="
							color: #7dd3fc;
							font-size: 40px;
							font-weight: 600;
							margin-bottom: 24px;
							max-width: 1000px;
						">
							${params.subtitle}
						</div>
					` : ''}
					${params.description ? `
						<div style="
							color: #cbd5e1;
							font-size: 24px;
							max-width: 1000px;
							line-height: 1.5;
							margin-bottom: 32px;
						">
							${params.description}
						</div>
					` : ''}
					${params.content ? `
						<div style="
							color: #e2e8f0;
							font-size: 20px;
							max-width: 900px;
							line-height: 1.4;
							margin-bottom: 32px;
						">
							${params.content}
						</div>
					` : ''}
					<div style="
						color: #94a3b8;
						font-size: 18px;
						margin-top: 40px;
					">anithing.moe</div>
				</div>
			</div>
		</div>
	`;
}

export async function GET({ url, setHeaders }) {
	try {
		// Extract parameters from URL
		const type = url.searchParams.get('type') || 'static';
		const title = url.searchParams.get('title');
		const subtitle = url.searchParams.get('subtitle');
		const description = url.searchParams.get('description');
		const content = url.searchParams.get('content');

		// Parameters for caching
		const params = {
			type,
			...(title && { title }),
			...(subtitle && { subtitle }),
			...(description && { description }),
			...(content && { content })
		};

		// Generate cache key and set headers
		const cacheKey = generateCacheKey(params);
		setHeaders({
			'Cache-Control': CACHE_CONTROL,
			'ETag': `"${cacheKey}"`,
			'Content-Type': 'image/png'
		});

		// Load font
		const fontFile = await fetch('http://pixeldrain.com/api/file/52yBhNXR');
		if (!fontFile.ok) {
			throw new Error('Failed to load font');
		}
		const fontData = await fontFile.arrayBuffer();

		// Choose template based on type
		let htmlString;
		if (type === 'dynamic') {
			// Simple dynamic template for testing
			htmlString = `
				<div style="
					display: flex;
					width: 100%;
					height: 100%;
					background: linear-gradient(135deg, #111827 0%, #581c87 50%, #111827 100%);
					justify-content: center;
					align-items: center;
				">
					<div style="
						color: white;
						font-size: 72px;
						font-weight: bold;
						text-align: center;
					">
						${title || 'anithing.moe'}
					</div>
				</div>
			`;
		} else {
			// Default to static template
			htmlString = getStaticTemplate();
		}

		// Convert HTML to Satori-compatible object
		const element = toReactNode(htmlString);

		// Generate SVG using Satori
		const svg = await satori(element, {
			width,
			height,
			fonts: [
				{
					name: 'Gilroy-SemiBold',
					data: fontData,
					weight: 600,
					style: 'normal'
				}
			]
		});

		// Convert SVG to PNG
		const resvg = new Resvg(svg, {
			fitTo: {
				mode: 'width',
				value: width
			}
		});

		const image = resvg.render();

		return new Response(image.asPng(), {
			headers: {
				'Content-Type': 'image/png',
				'Cache-Control': CACHE_CONTROL,
				'ETag': `"${cacheKey}"`
			}
		});

	} catch (error) {
		console.error('Error generating OG image:', error);
		return new Response('Error generating image', { status: 500 });
	}
}